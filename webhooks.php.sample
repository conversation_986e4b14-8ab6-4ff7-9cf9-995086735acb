<?php

$baseDir = '/root/ctc-docker';

$secret = '1qaz2wsx3edc';

$branch = 'master';

$requestBody = file_get_contents('php://input');

if (empty($requestBody)) {
    header('HTTP/1.1 400 Bad Request');
    die('Bad Request');
}

$payload = json_decode($requestBody, true);

if (!isset($payload['timestamp']) || !isset($payload['sign'])) {
    header('HTTP/1.1 400 Bad Request');
    die('Invalid Payload');
}

$content = "{$payload['timestamp']}\n{$secret}";

$mySign = base64_encode(hash_hmac('sha256', $content, $secret, true));

if ($payload['sign'] != $mySign) {
    header('HTTP/1.1 403 Permission Denied');
    die('Permission Denied');
}

if ($payload['ref'] == "refs/heads/{$branch}" && $payload['total_commits_count'] > 0) {

    $result = shell_exec("bash {$baseDir}/upgrade.sh");

    echo $result;
}