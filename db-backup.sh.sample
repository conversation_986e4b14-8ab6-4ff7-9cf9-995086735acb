#!/usr/bin/env bash

#备份保留天数
KEEP_DAYS=15

#COSCMD命令路径（绝对路径）
COS_CMD=/usr/local/bin/coscmd

#COS配置文件路径（绝对路径）
COS_CONF_PATH=/root/.cos.conf

#本地目录（绝对路径，末尾不带"/"）
LOCAL_DIR=/root/ctc-docker/mysql/data/backup

#远程目录（绝对路径，末尾不带"/"）
REMOTE_DIR=/backup/database

#导出环境变量
if [ -f .env ]; then
  export $(grep -v '^#' .env | xargs)
else
  echo "error: .env file not found"
  exit 1
fi

docker exec -i ctc-mysql bash <<'EOF'

#备份目录（末尾不带"/"）
backup_dir=/var/lib/mysql/backup

#创建备份目录
if [ ! -d ${backup_dir} ]; then
  mkdir -p ${backup_dir}
fi

#导出数据
mysqldump --no-tablespaces -u ${MYSQL_USER} -p${MYSQL_PASSWORD} ${MYSQL_DATABASE} | gzip > ${backup_dir}/${MYSQL_DATABASE}-$(date +%Y-%m-%d).sql.gz

exit
EOF

#删除过期备份
find ${LOCAL_DIR} -mtime +${KEEP_DAYS} -name "*.sql.gz" | xargs rm -f

#同步备份
echo y | ${COS_CMD} -c ${COS_CONF_PATH} upload -rs --delete ${LOCAL_DIR}/ ${REMOTE_DIR}/
