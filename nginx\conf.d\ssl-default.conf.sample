server {

    listen 80;

    server_name ctc.docker;

    return 301 https://$server_name$request_uri;
}

server {

    listen 443 ssl http2;

    server_name ctc.docker;

    ssl_certificate /etc/nginx/ssl/ctc.docker.crt;
    ssl_certificate_key /etc/nginx/ssl/ctc.docker.key;
    ssl_session_timeout 20m;
    ssl_session_cache shared:SSL:10m;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_prefer_server_ciphers on;

    root /var/www/html/ctc/public;
    index index.php index.html index.htm;
    charset utf-8;

    access_log /var/log/nginx/ctc.access.log;
    error_log /var/log/nginx/ctc.error.log;

    client_max_body_size 10M;
    fastcgi_read_timeout 600;
    fastcgi_send_timeout 600;

    location / {
        try_files $uri $uri/ /index.php?_url=$uri&$args;
    }

    location /wss {
        proxy_pass http://php:8282;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";
        proxy_set_header X-Real-IP $remote_addr;
    }

    location ~ \.php$ {

        include fastcgi_params;

        fastcgi_pass php:9000;
        fastcgi_index /index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }

    location ~ /\.(ht|git) {
        deny all;
        return 403;
    }

    location ~* \.(js|css|png|jpg|jpeg|webp|gif|ico)$ {
        expires max;
        log_not_found off;
        access_log off;
    }

}
